#pragma once

#include "JsonUtil.hpp"
#include <string>

struct Product
{
    int id;
    std::string name;
    std::string description;
    std::string begin_time;
    std::string end_time;
    std::string group_name;
};

void from_json(const nlohmann::json &j, Product &p)
{
    j["id"].get_to(p.id);
    j["productName"].get_to(p.name);
    p.description = JsonUtil::get<std::string>(j, "productDescription", "");
    p.begin_time = JsonUtil::get<std::string>(j, "productStartTime", "");
    p.end_time = JsonUtil::get<std::string>(j, "productEndTime", "");
    p.group_name = JsonUtil::get<std::string>(j, "groupName", "");
}
void to_json(nlohmann::json &j, const Product &p)
{
    j = nlohmann::json{
        { "id", p.id },
        { "description", p.description },
        { "name", p.name },
        { "begin_time", p.begin_time },
        { "end_time", p.end_time },
        { "group_name", p.group_name },
    };
}