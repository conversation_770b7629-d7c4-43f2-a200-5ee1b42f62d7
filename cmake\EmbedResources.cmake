# Function to convert a file to a C++ byte array
function(file_to_hex input_file output_var)
    file(READ ${input_file} hex_content HEX)
    string(REGEX REPLACE "([0-9a-f][0-9a-f])" "0x\\1," hex_content ${hex_content})
    set(${output_var} ${hex_content} PARENT_SCOPE)
endfunction()

# Function to get MIME type based on file extension
function(get_mime_type file mime_type)
    get_filename_component(ext ${file} EXT)
    string(TOLOWER "${ext}" ext)
    
    if(ext STREQUAL ".html" OR ext STREQUAL ".htm")
        set(${mime_type} "text/html" PARENT_SCOPE)
    elseif(ext STREQUAL ".css")
        set(${mime_type} "text/css" PARENT_SCOPE)
    elseif(ext STREQUAL ".js")
        set(${mime_type} "application/javascript" PARENT_SCOPE)
    elseif(ext STREQUAL ".png")
        set(${mime_type} "image/png" PARENT_SCOPE)
    elseif(ext STREQUAL ".jpg" OR ext STREQUAL ".jpeg")
        set(${mime_type} "image/jpeg" PARENT_SCOPE)
    elseif(ext STREQUAL ".gif")
        set(${mime_type} "image/gif" PARENT_SCOPE)
    elseif(ext STREQUAL ".svg")
        set(${mime_type} "image/svg+xml" PARENT_SCOPE)
    elseif(ext STREQUAL ".ico")
        set(${mime_type} "image/x-icon" PARENT_SCOPE)
    elseif(ext STREQUAL ".json")
        set(${mime_type} "application/json" PARENT_SCOPE)
    elseif(ext STREQUAL ".xml")
        set(${mime_type} "application/xml" PARENT_SCOPE)
    elseif(ext STREQUAL ".txt")
        set(${mime_type} "text/plain" PARENT_SCOPE)
    else()
        set(${mime_type} "application/octet-stream" PARENT_SCOPE)
    endif()
endfunction()

# Main function to embed resources
function(embed_resources output_header web_dir)
    # Get absolute paths
    get_filename_component(output_header ${output_header} ABSOLUTE)
    get_filename_component(web_dir ${web_dir} ABSOLUTE)

    # Get base directory for output files
    get_filename_component(output_dir ${output_header} DIRECTORY)
    get_filename_component(base_name ${output_header} NAME_WE)

    # Calculate source file path - it should be in the build directory structure
    get_filename_component(build_dir ${output_dir} DIRECTORY)
    get_filename_component(build_dir ${build_dir} DIRECTORY)
    set(output_source "${build_dir}/src/resources/${base_name}.cpp")

    # Ensure output directories exist
    file(MAKE_DIRECTORY "${output_dir}")
    get_filename_component(src_dir ${output_source} DIRECTORY)
    file(MAKE_DIRECTORY "${src_dir}")

    message(STATUS "Generating resources to:")
    message(STATUS "  Header: ${output_header}")
    message(STATUS "  Source: ${output_source}")

    # Create header file with declarations only
    file(WRITE ${output_header} "#pragma once\n\n")
    file(APPEND ${output_header} "#include <string>\n")
    file(APPEND ${output_header} "#include <unordered_map>\n\n")
    file(APPEND ${output_header} "namespace EmbeddedResources {\n\n")
    file(APPEND ${output_header} "struct Resource {\n")
    file(APPEND ${output_header} "    const unsigned char* data;\n")
    file(APPEND ${output_header} "    size_t size;\n")
    file(APPEND ${output_header} "    std::string mime_type;\n")
    file(APPEND ${output_header} "};\n\n")
    file(APPEND ${output_header} "extern const std::unordered_map<std::string, Resource> resources;\n\n")
    file(APPEND ${output_header} "const Resource* getResource(const std::string& path);\n\n")
    file(APPEND ${output_header} "} // namespace EmbeddedResources\n")

    # Create source file with definitions
    file(WRITE ${output_source} "#include \"resources/${base_name}.hpp\"\n\n")
    file(APPEND ${output_source} "namespace EmbeddedResources {\n\n")

    # Get all files in web directory
    file(GLOB_RECURSE web_files RELATIVE ${web_dir} ${web_dir}/*)
    
    # Process each file
    foreach(file ${web_files})
        # Skip directories
        if(IS_DIRECTORY ${web_dir}/${file})
            continue()
        endif()
        
        # Skip empty files
        file(SIZE ${web_dir}/${file} file_size)
        if(file_size EQUAL 0)
            continue()
        endif()
        
        # Convert file to hex
        file_to_hex(${web_dir}/${file} hex_content)
        
        # Remove trailing comma from hex content
        string(REGEX REPLACE ",$" "" hex_content "${hex_content}")
        
        # Get MIME type
        get_mime_type(${file} mime_type)
        
        # Create variable name
        string(MAKE_C_IDENTIFIER "${file}" var_name)
        
        # Write data array
        file(APPEND ${output_source} "static const unsigned char ${var_name}_data[] = {\n    ${hex_content}\n};\n\n")
    endforeach()
    
    # Start resources map
    file(APPEND ${output_source} "const std::unordered_map<std::string, Resource> resources = {\n")
    
    # Add each file to the map
    foreach(file ${web_files})
        if(NOT IS_DIRECTORY ${web_dir}/${file})
            string(MAKE_C_IDENTIFIER "${file}" var_name)
            get_mime_type(${file} mime_type)
            file(SIZE ${web_dir}/${file} file_size)
            
            # Convert Windows path separators to forward slashes for web paths
            string(REPLACE "\\" "/" web_path "${file}")
            file(APPEND ${output_source} "    {\"/${web_path}\", {${var_name}_data, ${file_size}, \"${mime_type}\"}},\n")
        endif()
    endforeach()
    
    # Close resources map and namespace
    file(APPEND ${output_source} "};\n\n")

    # Add getResource function implementation
    file(APPEND ${output_source} "const Resource* getResource(const std::string& path) {\n")
    file(APPEND ${output_source} "    auto it = resources.find(path);\n")
    file(APPEND ${output_source} "    return (it != resources.end()) ? &it->second : nullptr;\n")
    file(APPEND ${output_source} "}\n\n")

    file(APPEND ${output_source} "} // namespace EmbeddedResources\n")
endfunction()

# When called as a script, execute the function
if(CMAKE_SCRIPT_MODE_FILE)
    embed_resources("${OUTPUT_FILE}" "${WEB_DIR}")
endif()