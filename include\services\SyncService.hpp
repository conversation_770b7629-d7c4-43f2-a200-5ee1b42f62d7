#pragma once

#include "config/Config.hpp"
#include "dao/CustomerDao.hpp"
#include "dao/HolidayDao.hpp"
#include "dao/ProductDao.hpp"
#include "dao/ProjectDao.hpp"
#include "dao/SyncStatusDao.hpp"
#include "dao/UserDao.hpp"
#include "dao/WorkHourDao.hpp"
#include "http/HttpClient.hpp"
#include "models/Customer.hpp"
#include "models/Holiday.hpp"
#include "models/JsonUtil.hpp"
#include "models/Product.hpp"
#include "models/Project.hpp"
#include "models/SyncStatus.hpp"
#include "models/User.hpp"
#include "models/WorkHour.hpp"
#include <algorithm>
#include <asio.hpp>
#include <atomic>
#include <chrono>
#include <ctime>
#include <fmt/format.h>
#include <httplib.h>
#include <iomanip>
#include <map>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>
#include <sstream>
#include <string>
#include <thread>
#include <vector>

// Forward declaration
class Application;

class SyncService
{
public:
    SyncService(Application &application)
        : app_(application)
        , http_client_(app_.getConfig().backend)
    {
        checkAndSyncHolidays();
        startAutoSync();
    }

    ~SyncService()
    {
        if (auto_sync_running_)
            stopAutoSync();
    }

public:
    std::vector<SyncStatus> getSyncStatus()
    {
        return app_.getSyncStatusDao().findAll();
    }

    bool submitWorkHour(const nlohmann::json &workHourData)
    {
        SPDLOG_INFO("API POST /atom/v1/workhour/api/task/submitWorkHour");

        try
        {
            auto response = http_client_.post("/atom/v1/workhour/api/task/submitWorkHour", workHourData);
            if (!response)
            {
                SPDLOG_ERROR("Work hour submission failed - Code: {}, Message: {}", response.code, response.msg);
                return false;
            }

            // Check if the response data indicates success
            if (!response.data.empty())
            {
                try
                {
                    if (response.data.contains("code"))
                    {
                        int responseCode = response.data["code"];
                        if (responseCode != 0 && responseCode != 200)
                        {
                            std::string responseMsg = response.data.value("msg", "Unknown error");
                            SPDLOG_ERROR("SyncService: Work hour submission failed - API returned error code: {}, message: {}", responseCode, responseMsg);
                            return false;
                        }
                    }
                }
                catch (const std::exception &e)
                {
                    SPDLOG_WARN("SyncService: Could not parse response data: {}", e.what());
                }
            }

            SPDLOG_INFO("SyncService: Work hour submission completed successfully");

            // Immediately sync WorkHour data to ensure user can see submitted data
            SPDLOG_INFO("SyncService: Starting immediate WorkHour sync after successful submission...");
            syncWorkhours();
            SPDLOG_INFO("SyncService: Immediate WorkHour sync completed");

            return true;
        }
        catch (const std::exception &e)
        {
            SPDLOG_ERROR("SyncService: Work hour submission exception - {}", e.what());
            return false;
        }
    }

private:
    void startAutoSync()
    {
        if (auto_sync_running_)
        {
            return;
        }

        SPDLOG_INFO("Starting automatic synchronization service");
        auto_sync_running_ = true;
        io_context_ = std::make_unique<asio::io_context>();
        unified_sync_timer_ = std::make_unique<asio::steady_timer>(*io_context_);

        syncData();

        // Start IO context in background thread
        io_thread_ = std::make_unique<std::thread>([this]() {
            try
            {
                io_context_->run();
            }
            catch (const std::exception &e)
            {
                SPDLOG_ERROR("ASIO IO thread exception: {}", e.what());
            }
        });
    }

    void stopAutoSync()
    {
        auto_sync_running_ = false;

        // Cancel timer
        if (unified_sync_timer_)
        {
            unified_sync_timer_->cancel();
        }

        // Stop IO context
        if (io_context_)
        {
            io_context_->stop();
        }

        // Join threads
        if (auto_sync_thread_.joinable())
        {
            auto_sync_thread_.join();
        }
        if (io_thread_ && io_thread_->joinable())
        {
            io_thread_->join();
        }

        // Reset ASIO components
        unified_sync_timer_.reset();
        io_context_.reset();
        io_thread_.reset();
    }

    bool syncProducts()
    {
        auto response = http_client_.get("/atom/v1/workhour/api/product/list");
        if (!response)
        {
            SPDLOG_ERROR("Product sync failed - Code: {}, Message: {}", response.code, response.msg);
            return false;
        }

        std::vector<Product> products = response.data;
        if (app_.getProductDao().batchInsertOrUpdate(products))
            recordSyncStatus("product");

        return true;
    }

    bool syncProjects()
    {
        auto response = http_client_.get("/atom/v1/workhour/api/project/list");

        if (!response)
        {
            SPDLOG_ERROR("Project sync failed - Code: {}, Message: {}", response.code, response.msg);
            return false;
        }

        std::vector<Project> projects = response.data;
        if (app_.getProjectDao().batchInsertOrUpdate(projects))
            recordSyncStatus("project");
        return true;
    }

    bool syncCustomers()
    {
        auto response = http_client_.get("/atom/v1/workhour/api/customer/list");

        if (!response)
        {
            SPDLOG_ERROR("Customer sync failed - Code: {}, Message: {}", response.code, response.msg);
            return false;
        }

        std::vector<Customer> customers = response.data;
        if (app_.getCustomerDao().batchInsertOrUpdate(customers))
            recordSyncStatus("customer");
        return true;
    }

    bool syncUsers()
    {
        auto response = http_client_.get("/atom/v1/atomRbacService/user/listPage");
        if (!response)
        {
            SPDLOG_ERROR("User sync failed - Code: {}, Message: {}", response.code, response.msg);
            return false;
        }

        try
        {
            std::vector<User> users;
            response.data["list"].get_to(users);
            if (app_.getUserDao().batchInsertOrUpdate(users))
                recordSyncStatus("user");
        }
        catch (std::exception &e)
        {
            SPDLOG_ERROR("{}", e.what());
        }
        return true;
    }

    bool syncWorkHours(long long startTime = 0, long long endTime = 0)
    {
        auto now = std::chrono::system_clock::now();
        if (startTime == 0)
        {
            std::tm tm = {};
            tm.tm_year = 2024 - 1900;
            tm.tm_mon = 10 - 1;
            tm.tm_mday = 1;
            tm.tm_hour = 0;
            tm.tm_min = 0;
            tm.tm_sec = 0;
            auto start = std::chrono::system_clock::from_time_t(std::mktime(&tm));
            startTime = std::chrono::duration_cast<std::chrono::milliseconds>(start.time_since_epoch()).count();
        }
        if (endTime == 0)
        {
            endTime = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
        }

        std::string endpoint = fmt::format("/atom/v1/workhour/api/task/listMySubmitTaskWorkHour?pageSize=5000&startTime={}&endTime={}", startTime, endTime);
        auto response = http_client_.get(endpoint);
        if (!response)
        {
            SPDLOG_ERROR("Work hour sync failed - Code: {}, Message: {}", response.code, response.msg);
            return false;
        }

        auto workHours = parseWorkHours(response.data);
        if (app_.getWorkHourDao().batchInsertOrUpdate(workHours))
            recordSyncStatus("workhour", endTime);
        return true;
    }

    bool syncHolidays(int year)
    {
        const auto &config = app_.getConfig().holiday;
        const auto &url = fmt::format(fmt::runtime(config.source), year);
        SPDLOG_INFO("Holiday API GET {}", url);

        // Parse URL to extract host and path
        auto slash = url.find('/', 8);
        auto host = url.substr(0, slash);
        auto path = url.substr(slash);

        // Create HTTP client with configuration-based settings
        httplib::Client client(host);
        client.set_connection_timeout(config.timeout.connection);
        client.set_read_timeout(config.timeout.read);
        client.set_write_timeout(config.timeout.write);
        client.set_compress(true);
        client.set_follow_location(true);  // Follow redirects
        auto result = client.Get(path);
        if (!result || result->status != 200)
        {
            SPDLOG_ERROR("Request {} failed", url);
            return false;
        }

        try
        {
            auto response_data = nlohmann::json::parse(result->body);
            auto holidays = parseHolidays(response_data);

            if (holidays.empty())
            {
                return true;
            }

            auto &dao = app_.getHolidayDao();
            dao.deleteByYear(year);
            if (bool success = dao.batchInsert(holidays); success)
            {
                recordSyncStatus("holiday_" + std::to_string(year));
                return true;
            }
            else
            {
                SPDLOG_ERROR("Holiday sync success for year {}, but database insert failed", year);
                return false;
            }
        }
        catch (const std::exception &e)
        {
            SPDLOG_ERROR("Failed to parse JSON from {}: {}", url, e.what());
            return false;
        }
    }

    SyncStatus getLastSyncStatus(const std::string &data_type)
    {
        auto result = app_.getSyncStatusDao().findByDataType(data_type);
        if (result.has_value())
        {
            return result.value();
        }

        SyncStatus empty_status;
        empty_status.data_type = data_type;
        return empty_status;
    }

    // Helper function to safely extract values from JSON

private:
    void checkAndSyncHolidays()
    {
        SPDLOG_INFO("SyncService: Checking holiday data completeness");

        std::time_t now = std::time(nullptr);
        std::tm *tm = std::localtime(&now);
        int current_year = tm->tm_year + 1900;

        auto &dao = app_.getHolidayDao();
        auto existing_years = dao.getExistingYears();

        bool overall_success = true;
        for (int offset = -1; offset <= 1; ++offset)
        {
            auto year = current_year + offset;
            auto existed = std::find(existing_years.begin(), existing_years.end(), year) == existing_years.end();
            if (existed)
                continue;
            syncHolidays(year);
        }
    }

    void syncData()
    {
        if (!unified_sync_timer_ || !auto_sync_running_)
        {
            return;
        }

        syncProducts();
        syncProjects();
        syncCustomers();
        syncUsers();
        syncWorkhours();

        // Schedule next complete sync in 12 hours
        unified_sync_timer_->expires_after(std::chrono::hours(8));
        unified_sync_timer_->async_wait([this](const asio::error_code &ec) {
            if (!ec && auto_sync_running_)
            {
                syncData();
            }
            else if (ec != asio::error::operation_aborted)
            {
                SPDLOG_ERROR("SyncService: Unified sync timer error: {}", ec.message());
            }
        });
    }

    void syncWorkhours()
    {
        syncWorkHours(getLastSyncTime("workhour"));
    }

    long long getLastSyncTime(const std::string &dataType)
    {
        auto status = app_.getSyncStatusDao().findByDataType(dataType);
        if (!status)
            return 0;

        return status.value().sync_time;
    }

    void recordSyncStatus(const std::string &data_type, long long endTime = 0)
    {
        auto now = std::chrono::system_clock::now();
        SyncStatus status;
        status.data_type = data_type;
        status.sync_time = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
        auto &dao = app_.getSyncStatusDao();
        dao.insertOrUpdate(status);
    }

    std::vector<WorkHour> parseWorkHours(const nlohmann::json &data)
    {
        std::vector<WorkHour> workHours;

        try
        {
            // data is already the inner data object from the API response
            // We need to parse it as WorkHourData, not WorkHourApiResponse
            if (data.contains("list") && data["list"].is_array())
            {
                for (const auto &remoteWorkHourJson : data["list"])
                {
                    // Parse each work hour entry
                    long long employee_id = JsonUtil::get<long long>(remoteWorkHourJson, "employeeId", 0);
                    std::string employee_name = JsonUtil::get<std::string>(remoteWorkHourJson, "employeeName");
                    long long submit_date = JsonUtil::get<long long>(remoteWorkHourJson, "submitDate", 0);
                    long long create_time = JsonUtil::get<long long>(remoteWorkHourJson, "createTime", 0);

                    // Parse time consumed list
                    if (remoteWorkHourJson.contains("timeConsumedList") && remoteWorkHourJson["timeConsumedList"].is_array())
                    {
                        for (const auto &timeConsumedJson : remoteWorkHourJson["timeConsumedList"])
                        {
                            WorkHour workHour;

                            // Use the timeConsumedList item's id as unique identifier
                            workHour.id = JsonUtil::get<int>(timeConsumedJson, "id", 0);
                            workHour.user_id = std::to_string(employee_id);
                            workHour.user_name = employee_name;

                            // Handle projectId which can be null
                            if (timeConsumedJson.contains("projectId") && !timeConsumedJson["projectId"].is_null())
                            {
                                workHour.project_id = JsonUtil::get<int>(timeConsumedJson, "projectId", 0);
                            }
                            else
                            {
                                workHour.project_id = 0;  // Default to 0 for null projectId
                            }

                            workHour.duration_hours = JsonUtil::get<double>(timeConsumedJson, "submitWorkHour", 0.0) / 60.0;  // Convert minutes to hours
                            workHour.work_description = JsonUtil::get<std::string>(timeConsumedJson, "submitComment");

                            // Determine work type based on type field
                            int work_type = JsonUtil::get<int>(timeConsumedJson, "type", 2);
                            switch (work_type)
                            {
                            case 0:
                                workHour.work_type = "Management";
                                break;
                            case 1:
                                workHour.work_type = "Meeting";
                                break;
                            case 2:
                                workHour.work_type = "Development";
                                break;
                            case 3:
                                workHour.work_type = "Testing";
                                break;
                            case 4:
                                workHour.work_type = "Operations";
                                break;
                            default:
                                workHour.work_type = "Development";
                                break;
                            }

                            // Convert timestamp to date string
                            if (submit_date > 0)
                            {
                                auto tmt = static_cast<time_t>(submit_date / 1000);
                                std::tm *tm = std::gmtime(&tmt);
                                char date_str[11];
                                std::strftime(date_str, sizeof(date_str), "%Y-%m-%d", tm);
                                workHour.work_date = date_str;
                            }

                            // Use the timeConsumedList item's createTime and updateTime
                            long long item_create_time = JsonUtil::get<long long>(timeConsumedJson, "createTime", create_time);
                            long long item_update_time = JsonUtil::get<long long>(timeConsumedJson, "updateTime", create_time);

                            if (item_create_time > 0)
                            {
                                auto created_time_t = static_cast<time_t>(item_create_time / 1000);
                                std::tm *created_tm = std::gmtime(&created_time_t);
                                char created_str[20];
                                std::strftime(created_str, sizeof(created_str), "%Y-%m-%d %H:%M:%S", created_tm);
                                workHour.created_time = created_str;
                            }

                            if (item_update_time > 0)
                            {
                                auto updated_time_t = static_cast<time_t>(item_update_time / 1000);
                                std::tm *updated_tm = std::gmtime(&updated_time_t);
                                char updated_str[20];
                                std::strftime(updated_str, sizeof(updated_str), "%Y-%m-%d %H:%M:%S", updated_tm);
                                workHour.updated_time = updated_str;
                            }

                            SPDLOG_DEBUG("SyncService: Parsed work hour - ID: {}, User: {}, Project: {}, Hours: {}, Description: {}", workHour.id,
                                workHour.user_name, workHour.project_id, workHour.duration_hours, workHour.work_description);

                            workHours.push_back(workHour);
                        }
                    }
                }
                SPDLOG_INFO("SyncService: Successfully parsed {} work hours", workHours.size());
            }
            else
            {
                SPDLOG_WARN("SyncService: No work hour list found in response data");
            }
        }
        catch (const std::exception &e)
        {
            SPDLOG_ERROR("Error parsing work hours response: {}", e.what());
        }

        return workHours;
    }

    std::vector<Holiday> parseHolidays(const nlohmann::json &data)
    {
        std::vector<Holiday> holidays;

        if (!data.contains("year") || !data.contains("days"))
        {
            SPDLOG_ERROR("SyncService: Invalid holiday data format - missing year or days");
            return holidays;
        }

        int year = JsonUtil::get<int>(data, "year", 0);
        if (year == 0)
        {
            SPDLOG_ERROR("SyncService: Invalid year in holiday data");
            return holidays;
        }

        if (data["days"].is_array())
        {
            for (const auto &dayJson : data["days"])
            {
                Holiday holiday;
                holiday.year = year;
                holiday.name = JsonUtil::get<std::string>(dayJson, "name");
                holiday.date = JsonUtil::get<std::string>(dayJson, "date");
                holiday.is_off_day = JsonUtil::get<bool>(dayJson, "isOffDay", false);

                if (!holiday.name.empty() && !holiday.date.empty())
                {
                    holidays.push_back(holiday);
                }
            }
            SPDLOG_INFO("SyncService: Successfully parsed {} holidays for year {}", holidays.size(), year);
        }
        else
        {
            SPDLOG_WARN("SyncService: No holiday days array found in response data");
        }

        return holidays;
    }

private:
    // Reference to Application instance for accessing other components
    Application &app_;
    std::atomic<bool> auto_sync_running_{ false };
    std::thread auto_sync_thread_;

    // ASIO components for timer-based synchronization
    std::unique_ptr<asio::io_context> io_context_;
    std::unique_ptr<asio::steady_timer> unified_sync_timer_;
    std::unique_ptr<std::thread> io_thread_;

    HttpClient http_client_;
};
