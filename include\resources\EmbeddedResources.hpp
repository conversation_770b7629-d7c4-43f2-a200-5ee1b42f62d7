#pragma once

#include <string>
#include <unordered_map>

namespace EmbeddedResources {

struct Resource {
    const unsigned char* data;
    size_t size;
    std::string mime_type;
};

// Resource map will be defined in the generated source file
extern const std::unordered_map<std::string, Resource> resources;

// Helper function to get resource
inline const Resource* getResource(const std::string& path) {
    auto it = resources.find(path);
    return (it != resources.end()) ? &it->second : nullptr;
}

} // namespace EmbeddedResources