﻿cmake_minimum_required(VERSION 3.20)
project(daily_report VERSION 0.1.0)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

find_package(n<PERSON>hmann_json CONFIG REQUIRED)
find_package(httplib CONFIG REQUIRED)
find_package(SQLiteCpp CONFIG REQUIRED)
find_package(spdlog CONFIG REQUIRED)
find_package(OpenSSL REQUIRED)

include_directories(include)

# Enable HTTPS support for httplib
add_definitions(-DCPPHTTPLIB_OPENSSL_SUPPORT)

# Get source files (excluding the placeholder EmbeddedResources.cpp)
file(GLOB_RECURSE SRC_FILES
    "src/*.cpp"
    "include/*.hpp"
)

# Remove the placeholder EmbeddedResources.cpp from source files
list(FILTER SRC_FILES EXCLUDE REGEX "src/resources/EmbeddedResources\\.cpp$")

# Include our custom CMake module
include(${CMAKE_CURRENT_SOURCE_DIR}/cmake/EmbedResources.cmake)

# Set paths for generated resources
set(EMBEDDED_RESOURCES_HEADER "${CMAKE_CURRENT_BINARY_DIR}/include/resources/EmbeddedResources.hpp")
set(EMBEDDED_RESOURCES_SOURCE "${CMAKE_CURRENT_BINARY_DIR}/src/resources/EmbeddedResources.cpp")

# Create output directories
file(MAKE_DIRECTORY "${CMAKE_CURRENT_BINARY_DIR}/include/resources")
file(MAKE_DIRECTORY "${CMAKE_CURRENT_BINARY_DIR}/src/resources")

# Get all web files
file(GLOB_RECURSE WEB_FILES "${CMAKE_CURRENT_SOURCE_DIR}/web/*")

# Generate embedded resources using custom command
add_custom_command(
    OUTPUT ${EMBEDDED_RESOURCES_HEADER} ${EMBEDDED_RESOURCES_SOURCE}
    COMMAND ${CMAKE_COMMAND}
        -DCMAKE_CURRENT_SOURCE_DIR=${CMAKE_CURRENT_SOURCE_DIR}
        -DOUTPUT_FILE=${EMBEDDED_RESOURCES_HEADER}
        -DWEB_DIR=${CMAKE_CURRENT_SOURCE_DIR}/web
        -P ${CMAKE_CURRENT_SOURCE_DIR}/cmake/EmbedResources.cmake
    DEPENDS ${WEB_FILES} ${CMAKE_CURRENT_SOURCE_DIR}/cmake/EmbedResources.cmake
    COMMENT "Generating embedded resources"
)

# Define target for resource generation
add_custom_target(generate_resources
    DEPENDS ${EMBEDDED_RESOURCES_HEADER} ${EMBEDDED_RESOURCES_SOURCE}
)

# Add generated files to sources
list(APPEND SRC_FILES ${EMBEDDED_RESOURCES_SOURCE})

# Include generated header directory
include_directories("${CMAKE_CURRENT_BINARY_DIR}/include")

# Define EMBED_RESOURCES macro
add_compile_definitions(EMBED_RESOURCES)

# Create executable
add_executable(${PROJECT_NAME} ${SRC_FILES})

# Add dependency to ensure resources are generated before compilation
add_dependencies(${PROJECT_NAME} generate_resources)

if(WIN32)
    target_compile_options(${PROJECT_NAME} PRIVATE /bigobj)
endif()

target_link_libraries(${PROJECT_NAME}
    PRIVATE
    nlohmann_json::nlohmann_json
    httplib::httplib
    SQLiteCpp
    spdlog::spdlog_header_only
    OpenSSL::SSL OpenSSL::Crypto
)

# Copy configuration file to build directory
# add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
#     COMMAND ${CMAKE_COMMAND} -E copy_if_different
#     ${CMAKE_SOURCE_DIR}/config.json
#     $<TARGET_FILE_DIR:${PROJECT_NAME}>/config.json
#     COMMENT "Copying config.json to build directory"
# )